#!/bin/bash

# =============================================================================
# SCRIPT DE DÉMONSTRATION DE LA COMMUNICATION NEURONALE HANUMAN
# =============================================================================
#
# Ce script démontre l'utilisation du système de communication neuronale
# de l'organisme IA Hanuman avec des exemples pratiques.
#
# Auteur: Hanuman AI System
# Version: 1.0.0
# Date: $(date +%Y-%m-%d)
# =============================================================================

set -euo pipefail

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Variables globales
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
HANUMAN_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$HANUMAN_ROOT/config/neural-communication"

# =============================================================================
# FONCTIONS UTILITAIRES
# =============================================================================

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_neural() {
    echo -e "${PURPLE}[NEURAL]${NC} $1"
}

log_demo() {
    echo -e "${CYAN}[DEMO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# =============================================================================
# FONCTIONS DE DÉMONSTRATION
# =============================================================================

show_configuration_overview() {
    log_neural "🧠 APERÇU DE LA CONFIGURATION NEURONALE HANUMAN"
    echo ""

    log_info "📁 Structure des configurations:"
    if [[ -d "$CONFIG_DIR" ]]; then
        find "$CONFIG_DIR" -type f -name "*.json" | head -15 | while read file; do
            echo "  ├─ $(basename "$file")"
        done
    else
        log_error "Configuration non trouvée. Exécutez: ./scripts/configure-neural-communication.sh"
        return 1
    fi

    echo ""
    log_info "📊 Statistiques:"
    local agent_configs=$(find "$CONFIG_DIR/agents" -name "*.json" 2>/dev/null | wc -l || echo "0")
    echo "  - Agents configurés: $agent_configs"
    echo "  - Topics Kafka: 25+ topics organisés"
    echo "  - Canaux Redis: 10+ canaux Pub/Sub"
    echo "  - Subjects NATS: Hiérarchie complète"
}

demonstrate_kafka_topics() {
    log_demo "📡 DÉMONSTRATION DES TOPICS KAFKA"
    echo ""

    if [[ -f "$CONFIG_DIR/kafka/neural-topics.json" ]]; then
        log_info "Topics principaux configurés:"

        echo "🔥 Topics Core (Communication principale):"
        echo "  ├─ neural-signals (12 partitions) - Signaux neuronaux principaux"
        echo "  ├─ agent-heartbeat (6 partitions) - Battements de cœur des agents"
        echo "  ├─ task-coordination (8 partitions) - Coordination des tâches"
        echo "  ├─ memory-sync (4 partitions) - Synchronisation mémoire"
        echo "  └─ emergency-signals (3 partitions) - Signaux d'urgence"

        echo ""
        echo "🤖 Topics Agents:"
        echo "  ├─ agent.frontend.requests/responses"
        echo "  ├─ agent.backend.requests/responses"
        echo "  ├─ agent.security.alerts/scans"
        echo "  ├─ agent.qa.test-results/coverage"
        echo "  └─ agent.devops.deployments/infrastructure"

        echo ""
        echo "🧠 Topics Cortex Central:"
        echo "  ├─ cortex.instructions - Instructions du cortex"
        echo "  ├─ cortex.decisions - Décisions prises"
        echo "  ├─ cortex.learning - Données d'apprentissage"
        echo "  └─ cortex.evolution - Évolution du système"
    else
        log_error "Configuration Kafka non trouvée"
    fi
}

demonstrate_redis_channels() {
    log_demo "💾 DÉMONSTRATION DES CANAUX REDIS"
    echo ""

    log_info "Canaux Pub/Sub configurés:"
    echo "  ├─ neural:signals - Signaux neuronaux"
    echo "  ├─ neural:heartbeat - Battements de cœur"
    echo "  ├─ neural:emergency - Signaux d'urgence"
    echo "  ├─ agent:status - Statuts des agents"
    echo "  ├─ cortex:instructions - Instructions cortex"
    echo "  └─ system:health - Santé du système"

    echo ""
    log_info "Patterns de clés configurés:"
    echo "  ├─ agent:{agentId}:{type} - Données des agents"
    echo "  ├─ task:{taskId}:{status} - Tâches en cours"
    echo "  ├─ memory:{type}:{id} - Mémoire distribuée"
    echo "  └─ metric:{type}:{timestamp} - Métriques"

    echo ""
    log_info "TTL configurés:"
    echo "  ├─ agent-state: 300s (5 min)"
    echo "  ├─ task-data: 86400s (24h)"
    echo "  ├─ memory-short: 1800s (30 min)"
    echo "  └─ memory-long: 604800s (7 jours)"
}

demonstrate_agent_configurations() {
    log_demo "🤖 DÉMONSTRATION DES CONFIGURATIONS D'AGENTS"
    echo ""

    if [[ -d "$CONFIG_DIR/agents" ]]; then
        log_info "Agents configurés avec communication complète:"

        local agents=("frontend" "backend" "security" "qa" "devops" "performance" "uiux" "evolution")
        for agent in "${agents[@]}"; do
            echo "  ├─ Agent $agent:"
            echo "    ├─ Kafka: agent.$agent.requests/responses"
            echo "    ├─ Redis: $agent:notifications, $agent:requests"
            echo "    ├─ NATS: agent.$agent.>, neural.signals.$agent"
            echo "    └─ Monitoring: Santé + Métriques activés"
        done
    else
        log_error "Configurations d'agents non trouvées"
    fi
}

demonstrate_monitoring_setup() {
    log_demo "📊 DÉMONSTRATION DU MONITORING"
    echo ""

    log_info "Métriques configurées:"
    echo "📡 Communication:"
    echo "  ├─ messagesSent (counter) - Messages envoyés"
    echo "  ├─ messagesReceived (counter) - Messages reçus"
    echo "  ├─ messageLatency (histogram) - Latence des messages"
    echo "  └─ synapticStrength (gauge) - Force des connexions"

    echo ""
    echo "🤖 Agents:"
    echo "  ├─ activeAgents (gauge) - Agents actifs"
    echo "  ├─ agentHealth (gauge) - Santé des agents"
    echo "  └─ taskCompletion (counter) - Tâches complétées"

    echo ""
    echo "📈 Dashboards configurés:"
    echo "  ├─ Neural - Communication neuronale"
    echo "  ├─ Agents - État des agents Hanuman"
    echo "  └─ System - Métriques système"
}

show_usage_examples() {
    log_demo "💡 EXEMPLES D'UTILISATION"
    echo ""

    log_info "🚀 Démarrage du système:"
    echo "  ./scripts/start-neural-communication.sh"
    echo ""

    log_info "🛑 Arrêt du système:"
    echo "  ./scripts/stop-neural-communication.sh"
    echo ""

    log_info "🔧 Reconfiguration:"
    echo "  ./scripts/configure-neural-communication.sh"
    echo ""

    log_info "🧪 Tests:"
    echo "  ./scripts/test-neural-communication-config.sh"
    echo ""

    log_info "📖 Documentation:"
    echo "  cat $CONFIG_DIR/README.md"
    echo ""

    log_info "🎭 Cette démonstration:"
    echo "  ./scripts/demo-neural-communication.sh"
}

show_architecture_summary() {
    log_demo "🏗️ ARCHITECTURE DE LA COMMUNICATION NEURONALE"
    echo ""

    log_info "Le système implémente une architecture biomimétique avec:"
    echo ""
    echo "🧠 Cortex Central:"
    echo "  └─ Orchestrateur principal avec moteur de décision"
    echo ""
    echo "🔗 Synapses (Kafka):"
    echo "  └─ Communication asynchrone fiable entre agents"
    echo ""
    echo "💾 Mémoire Partagée (Redis):"
    echo "  └─ Cache rapide et notifications temps réel"
    echo ""
    echo "⚡ Signaux Rapides (NATS):"
    echo "  └─ Communication ultra-rapide avec JetStream"
    echo ""
    echo "🤖 Agents Spécialisés:"
    echo "  └─ 8 agents avec capacités dédiées"
    echo ""
    echo "📊 Monitoring Intégré:"
    echo "  └─ Métriques complètes et dashboards"
}

# =============================================================================
# FONCTION PRINCIPALE
# =============================================================================

main() {
    log_neural "🎭 DÉMONSTRATION DE LA COMMUNICATION NEURONALE HANUMAN"
    log_info "Timestamp: $(date)"
    log_info "Répertoire Hanuman: $HANUMAN_ROOT"
    echo ""

    # Vérification de l'existence des configurations
    if [[ ! -d "$CONFIG_DIR" ]]; then
        log_error "❌ Configurations non trouvées!"
        log_info "Exécutez d'abord: ./scripts/configure-neural-communication.sh"
        exit 1
    fi

    # Démonstrations
    show_configuration_overview
    echo ""

    show_architecture_summary
    echo ""

    demonstrate_kafka_topics
    echo ""

    demonstrate_redis_channels
    echo ""

    demonstrate_agent_configurations
    echo ""

    demonstrate_monitoring_setup
    echo ""

    show_usage_examples
    echo ""

    log_neural "🎉 Démonstration terminée!"
    log_info "Le système de communication neuronale Hanuman est prêt à être utilisé."
    log_info "Consultez la documentation complète dans: $CONFIG_DIR/README.md"
}

# Exécution du script principal
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
