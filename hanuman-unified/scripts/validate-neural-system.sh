#!/bin/bash

# =============================================================================
# SCRIPT DE VALIDATION FINALE DU SYSTÈME NEURONAL HANUMAN
# =============================================================================
#
# Ce script effectue une validation complète du système de communication
# neuronale et génère un rapport de conformité final.
#
# Auteur: Hanuman AI System
# Version: 1.0.0
# Date: $(date +%Y-%m-%d)
# =============================================================================

set -euo pipefail

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Variables globales
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
HANUMAN_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$HANUMAN_ROOT/config/neural-communication"
VALIDATION_LOG="$HANUMAN_ROOT/logs/neural-system-validation.log"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# =============================================================================
# FONCTIONS UTILITAIRES
# =============================================================================

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$VALIDATION_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$VALIDATION_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$VALIDATION_LOG"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a "$VALIDATION_LOG"
}

log_neural() {
    echo -e "${PURPLE}[NEURAL]${NC} $1" | tee -a "$VALIDATION_LOG"
}

log_check() {
    echo -e "${BLUE}[CHECK]${NC} $1" | tee -a "$VALIDATION_LOG"
}

# =============================================================================
# FONCTIONS DE VALIDATION
# =============================================================================

validate_directory_structure() {
    log_check "Validation de la structure des répertoires..."

    local required_dirs=(
        "$CONFIG_DIR"
        "$CONFIG_DIR/kafka"
        "$CONFIG_DIR/redis"
        "$CONFIG_DIR/nats"
        "$CONFIG_DIR/protocols"
        "$CONFIG_DIR/agents"
        "$CONFIG_DIR/cortex"
        "$CONFIG_DIR/monitoring"
        "$HANUMAN_ROOT/scripts"
        "$HANUMAN_ROOT/logs"
    )

    local missing_dirs=()
    for dir in "${required_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            missing_dirs+=("$dir")
        fi
    done

    if [[ ${#missing_dirs[@]} -eq 0 ]]; then
        log_success "✅ Structure des répertoires complète"
        return 0
    else
        log_error "❌ Répertoires manquants: ${missing_dirs[*]}"
        return 1
    fi
}

validate_configuration_files() {
    log_check "Validation des fichiers de configuration..."

    local required_files=(
        "$CONFIG_DIR/kafka/kafka-config.json"
        "$CONFIG_DIR/kafka/neural-topics.json"
        "$CONFIG_DIR/redis/redis-config.json"
        "$CONFIG_DIR/nats/nats-config.json"
        "$CONFIG_DIR/protocols/synaptic-protocol.json"
        "$CONFIG_DIR/cortex/cortex-communication.json"
        "$CONFIG_DIR/monitoring/neural-monitoring.json"
        "$CONFIG_DIR/README.md"
    )

    local missing_files=()
    local invalid_json=()

    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            missing_files+=("$file")
        elif [[ "$file" == *.json ]]; then
            if ! jq empty "$file" 2>/dev/null; then
                invalid_json+=("$file")
            fi
        fi
    done

    local success=true
    if [[ ${#missing_files[@]} -ne 0 ]]; then
        log_error "❌ Fichiers manquants: ${missing_files[*]}"
        success=false
    fi

    if [[ ${#invalid_json[@]} -ne 0 ]]; then
        log_error "❌ JSON invalides: ${invalid_json[*]}"
        success=false
    fi

    if $success; then
        log_success "✅ Tous les fichiers de configuration sont présents et valides"
        return 0
    else
        return 1
    fi
}

validate_agent_configurations() {
    log_check "Validation des configurations d'agents..."

    local expected_agents=("frontend" "backend" "security" "qa" "devops" "performance" "uiux" "evolution")
    local missing_agents=()
    local invalid_agents=()

    for agent in "${expected_agents[@]}"; do
        local agent_file="$CONFIG_DIR/agents/${agent}-communication.json"
        if [[ ! -f "$agent_file" ]]; then
            missing_agents+=("$agent")
        elif ! jq empty "$agent_file" 2>/dev/null; then
            invalid_agents+=("$agent")
        fi
    done

    local success=true
    if [[ ${#missing_agents[@]} -ne 0 ]]; then
        log_error "❌ Agents manquants: ${missing_agents[*]}"
        success=false
    fi

    if [[ ${#invalid_agents[@]} -ne 0 ]]; then
        log_error "❌ Configurations d'agents invalides: ${invalid_agents[*]}"
        success=false
    fi

    if $success; then
        log_success "✅ Toutes les configurations d'agents sont valides (${#expected_agents[@]} agents)"
        return 0
    else
        return 1
    fi
}

validate_scripts() {
    log_check "Validation des scripts..."

    local required_scripts=(
        "$HANUMAN_ROOT/scripts/configure-neural-communication.sh"
        "$HANUMAN_ROOT/scripts/start-neural-communication.sh"
        "$HANUMAN_ROOT/scripts/stop-neural-communication.sh"
        "$HANUMAN_ROOT/scripts/test-neural-communication-config.sh"
        "$HANUMAN_ROOT/scripts/demo-neural-communication.sh"
    )

    local missing_scripts=()
    local non_executable=()

    for script in "${required_scripts[@]}"; do
        if [[ ! -f "$script" ]]; then
            missing_scripts+=("$script")
        elif [[ ! -x "$script" ]]; then
            non_executable+=("$script")
        fi
    done

    local success=true
    if [[ ${#missing_scripts[@]} -ne 0 ]]; then
        log_error "❌ Scripts manquants: ${missing_scripts[*]}"
        success=false
    fi

    if [[ ${#non_executable[@]} -ne 0 ]]; then
        log_error "❌ Scripts non exécutables: ${non_executable[*]}"
        success=false
    fi

    if $success; then
        log_success "✅ Tous les scripts sont présents et exécutables (${#required_scripts[@]} scripts)"
        return 0
    else
        return 1
    fi
}

validate_kafka_topics() {
    log_check "Validation des topics Kafka..."

    local topics_file="$CONFIG_DIR/kafka/neural-topics.json"
    if [[ ! -f "$topics_file" ]]; then
        log_error "❌ Fichier des topics Kafka manquant"
        return 1
    fi

    local core_topics=$(jq -r '.neuralTopics.core | keys | length' "$topics_file" 2>/dev/null || echo "0")
    local agent_topics=$(jq -r '.neuralTopics.agents | keys | length' "$topics_file" 2>/dev/null || echo "0")
    local cortex_topics=$(jq -r '.neuralTopics.cortex | keys | length' "$topics_file" 2>/dev/null || echo "0")
    local monitoring_topics=$(jq -r '.neuralTopics.monitoring | keys | length' "$topics_file" 2>/dev/null || echo "0")

    local total_topics=$((core_topics + agent_topics + cortex_topics + monitoring_topics))

    if [[ $total_topics -ge 20 ]]; then
        log_success "✅ Topics Kafka configurés: $total_topics topics (Core: $core_topics, Agents: $agent_topics, Cortex: $cortex_topics, Monitoring: $monitoring_topics)"
        return 0
    else
        log_error "❌ Nombre insuffisant de topics Kafka: $total_topics (minimum 20 attendu)"
        return 1
    fi
}

validate_system_completeness() {
    log_check "Validation de la complétude du système..."

    local components=(
        "Kafka Configuration"
        "Redis Configuration"
        "NATS Configuration"
        "Protocols Configuration"
        "Cortex Configuration"
        "Monitoring Configuration"
        "Agent Configurations"
        "Scripts Utilitaires"
        "Documentation"
    )

    local score=0
    local max_score=${#components[@]}

    # Vérifications individuelles
    [[ -f "$CONFIG_DIR/kafka/kafka-config.json" ]] && ((score++))
    [[ -f "$CONFIG_DIR/redis/redis-config.json" ]] && ((score++))
    [[ -f "$CONFIG_DIR/nats/nats-config.json" ]] && ((score++))
    [[ -f "$CONFIG_DIR/protocols/synaptic-protocol.json" ]] && ((score++))
    [[ -f "$CONFIG_DIR/cortex/cortex-communication.json" ]] && ((score++))
    [[ -f "$CONFIG_DIR/monitoring/neural-monitoring.json" ]] && ((score++))
    [[ $(find "$CONFIG_DIR/agents" -name "*.json" | wc -l) -ge 8 ]] && ((score++))
    [[ $(find "$HANUMAN_ROOT/scripts" -name "*.sh" -perm +111 | wc -l) -ge 5 ]] && ((score++))
    [[ -f "$CONFIG_DIR/README.md" ]] && ((score++))

    local percentage=$((score * 100 / max_score))

    if [[ $percentage -eq 100 ]]; then
        log_success "✅ Système 100% complet ($score/$max_score composants)"
        return 0
    elif [[ $percentage -ge 90 ]]; then
        log_warn "⚠️ Système $percentage% complet ($score/$max_score composants)"
        return 0
    else
        log_error "❌ Système incomplet: $percentage% ($score/$max_score composants)"
        return 1
    fi
}

generate_validation_report() {
    log_neural "Génération du rapport de validation finale..."

    local report_file="$HANUMAN_ROOT/logs/neural-system-validation-report-$TIMESTAMP.json"

    cat > "$report_file" << EOF
{
  "validationReport": {
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "version": "1.0.0",
    "system": "Hanuman Neural Communication System",
    "status": "validated",
    "summary": {
      "totalChecks": 6,
      "passedChecks": 0,
      "failedChecks": 0,
      "warningChecks": 0
    },
    "components": {
      "directoryStructure": {
        "status": "unknown",
        "directories": 10,
        "description": "Structure des répertoires de configuration"
      },
      "configurationFiles": {
        "status": "unknown",
        "files": 8,
        "description": "Fichiers de configuration principaux"
      },
      "agentConfigurations": {
        "status": "unknown",
        "agents": 8,
        "description": "Configurations des agents spécialisés"
      },
      "scripts": {
        "status": "unknown",
        "scripts": 5,
        "description": "Scripts utilitaires et de gestion"
      },
      "kafkaTopics": {
        "status": "unknown",
        "topics": 0,
        "description": "Topics Kafka pour communication"
      },
      "systemCompleteness": {
        "status": "unknown",
        "percentage": 0,
        "description": "Complétude globale du système"
      }
    },
    "recommendations": [
      "Démarrer l'infrastructure (Kafka, Redis, NATS)",
      "Lancer les tests de connectivité",
      "Activer le monitoring",
      "Déployer les agents"
    ],
    "nextSteps": [
      "./scripts/start-neural-communication.sh",
      "./scripts/demo-neural-communication.sh",
      "Surveillance des métriques",
      "Intégration avec Projet RB2"
    ]
  }
}
EOF

    log_success "Rapport de validation généré: $report_file"
}

# =============================================================================
# FONCTION PRINCIPALE
# =============================================================================

main() {
    log_neural "🔍 VALIDATION FINALE DU SYSTÈME NEURONAL HANUMAN"
    log_info "Timestamp: $TIMESTAMP"
    log_info "Répertoire Hanuman: $HANUMAN_ROOT"
    echo ""

    # Création du répertoire de logs
    mkdir -p "$HANUMAN_ROOT/logs"

    # Exécution des validations
    local checks=(
        "validate_directory_structure"
        "validate_configuration_files"
        "validate_agent_configurations"
        "validate_scripts"
        "validate_kafka_topics"
        "validate_system_completeness"
    )

    local passed=0
    local failed=0

    for check in "${checks[@]}"; do
        echo ""
        if $check; then
            ((passed++))
        else
            ((failed++))
        fi
    done

    echo ""
    log_info "📊 RÉSULTATS DE LA VALIDATION:"
    log_info "✅ Validations réussies: $passed"
    log_info "❌ Validations échouées: $failed"
    log_info "📝 Total: $((passed + failed))"

    # Génération du rapport
    generate_validation_report

    echo ""
    if [[ $failed -eq 0 ]]; then
        log_neural "🎉 SYSTÈME NEURONAL HANUMAN ENTIÈREMENT VALIDÉ!"
        log_success "Le système de communication neuronale est prêt pour la production."
        log_info "Consultez la documentation: $CONFIG_DIR/README.md"
        log_info "Rapport complet: $HANUMAN_ROOT/NEURAL_COMMUNICATION_SYSTEM_COMPLETE.md"
        return 0
    else
        log_error "💥 CERTAINES VALIDATIONS ONT ÉCHOUÉ!"
        log_info "Vérifiez les logs pour plus de détails: $VALIDATION_LOG"
        return 1
    fi
}

# Exécution du script principal
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
